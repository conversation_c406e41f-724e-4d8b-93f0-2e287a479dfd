- **Property:** display
- **Shorthand:** d
  Utilities for controlling the display type of an element.

```css
dB {
  display: block;
}
dI {
  display: inline;
}
dIb {
  display: inline-block;
}
dF {
  display: flex;
}
dG {
  display: grid;
}
dFr {
  display: flow-root;
}
dT {
  display: table;
}
dN {
  display: none;
}
```

The `display` property controls how an element is displayed and how it participates in layout. This is fundamental for creating layouts and controlling element behavior.

## Text Flow Display Types

**Example:**

```html
<p>
  When controlling the flow of text, using the CSS property
  <span class="dI">display: inline</span> will cause the text inside the element
  to wrap normally.
</p>
<p>
  While using the property <span class="dIb">display: inline-block</span> will
  wrap the element to prevent the text inside from extending beyond its parent.
</p>
<p>
  Lastly, using the property <span class="dB">display: block</span> will put the
  element on its own line and fill its parent.
</p>
```

![Text flow display example](./img/display/text-flow.png)

## Flex Display

```css
dF {
  display: flex;
}
```

**Example:**

```html
<div class="dF gap20px p20px bgcLightGray brd1px;solid;#333">
  <div class="p10px bgcLightBlue">Flex Item 1</div>
  <div class="p10px bgcLightGreen">Flex Item 2</div>
  <div class="p10px bgcLightCoral">Flex Item 3</div>
</div>
```

![Flex display example](./img/display/flex.png)

## Grid Display

```css
dG {
  display: grid;
}
```

**Example:**

```html
<div class="dG gtc1fr;1fr gap20px p20px bgcLightGray brd1px;solid;#333">
  <div class="p10px bgcLightBlue">Grid Item 1</div>
  <div class="p10px bgcLightGreen">Grid Item 2</div>
  <div class="p10px bgcLightCoral">Grid Item 3</div>
  <div class="p10px bgcLightYellow">Grid Item 4</div>
</div>
```

![Grid display example](./img/display/grid.png)

## Flow Root Display

```css
dFr {
  display: flow-root;
}
```

**Example:**

```html
<div class="dFr p20px bgcLightGray brd1px;solid;#333">
  <div class="flL w100px h100px bgcLightBlue">Floated element</div>
  <p>
    Flow-root creates a new block formatting context that contains floated
    elements.
  </p>
</div>
```

![Flow root display example](./img/display/flow-root.png)

## Table Display

```css
dT {
  display: table;
}
```

**Example:**

```html
<div class="dT w100% brd1px;solid;#333">
  <div class="dTr">
    <div class="dTcell p10px bgcLightBlue brd1px;solid;#333">Cell 1</div>
    <div class="dTcell p10px bgcLightGreen brd1px;solid;#333">Cell 2</div>
  </div>
</div>
```

![Table display example](./img/display/table.png)

## None Display

```css
dN {
  display: none;
}
```

**Example:**

```html
<div class="p10px bgcLightBlue">Visible element</div>
<div class="dN p10px bgcRed">Hidden element</div>
<div class="p10px bgcLightGreen">Another visible element</div>
```

![None display example](./img/display/none.png)

## Comparison

**Example:**

```html
<div class="dF fxdColumn gap10px">
  <div class="dB p10px bgcLightBlue brd1px;solid;#333">Block</div>
  <div>
    <span class="dI p5px bgcLightGreen brd1px;solid;#333">Inline</span>
    <span class="dI p5px bgcLightCoral brd1px;solid;#333">Inline</span>
  </div>
  <div class="dF gap10px">
    <div class="p10px bgcLightYellow brd1px;solid;#333">Flex Item 1</div>
    <div class="p10px bgcLightPink brd1px;solid;#333">Flex Item 2</div>
  </div>
  <div class="dG gtc1fr;1fr gap10px">
    <div class="p10px bgcLightCyan brd1px;solid;#333">Grid Item 1</div>
    <div class="p10px bgcLightSalmon brd1px;solid;#333">Grid Item 2</div>
  </div>
</div>
```

![Display comparison example](./img/display/comparison.png)
