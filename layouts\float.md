- **Property:** float
- **Shorthand:** fl  
  Utilities for controlling the float behavior of elements.

```css
flL {
  float: left;
}
flR {
  float: right;
}
flN {
  float: none;
}
flIs {
  float: inline-start;
}
flIe {
  float: inline-end;
}
```

The `float` property specifies how an element should float within its container. Floating elements are taken out of the normal document flow and positioned to the left or right of their container, allowing other content to wrap around them.

## Float Left

```css
flL {
  float: left;
}
```

**Example:**

```html
<div class="p20px bgcLightGray brd1px;solid;#333">
  <div class="flL w100px h100px bgcLightBlue brd1px;solid;#333 mR10px mB10px">
    Floated Left
  </div>
  <p>
    This text wraps around the floated element. The floated element is
    positioned to the left side of its container, and the text flows around it
    naturally. This is useful for creating layouts where you want text to wrap
    around images or other content blocks.
  </p>
  <p>
    Additional paragraphs continue to wrap around the floated element until it
    is cleared or the content extends beyond the height of the floated element.
  </p>
</div>
```

![Float left example](./img/float/float-left.png)

## Float Right

```css
flR {
  float: right;
}
```

**Example:**

```html
<div class="p20px bgcLightGray brd1px;solid;#333">
  <div class="flR w100px h100px bgcLightGreen brd1px;solid;#333 mL10px mB10px">
    Floated Right
  </div>
  <p>
    This text wraps around the right-floated element. The floated element is
    positioned to the right side of its container, and the text flows around it
    from the left. This creates a different visual layout compared to left
    floating.
  </p>
  <p>
    The text continues to flow around the floated element, creating a natural
    wrap effect that is commonly used in magazine-style layouts.
  </p>
</div>
```

![Float right example](./img/float/float-right.png)

## Float None

```css
flN {
  float: none;
}
```

**Example:**

```html
<div class="p20px bgcLightGray brd1px;solid;#333">
  <div class="flN w100px h100px bgcLightCoral brd1px;solid;#333 mB10px">
    No Float
  </div>
  <p>
    This element is not floated and remains in the normal document flow. It
    behaves like a regular block element, taking up the full width of its
    container and pushing subsequent content below it.
  </p>
</div>
```

![Float none example](./img/float/float-none.png)

## Inline Start and End Float

```css
flIs {
  float: inline-start;
}
flIe {
  float: inline-end;
}
```

The `inline-start` and `inline-end` values are logical properties that adapt to the writing direction of the document. In left-to-right languages, `inline-start` behaves like `left` and `inline-end` behaves like `right`.

**Example:**

```html
<div class="p20px bgcLightGray brd1px;solid;#333">
  <div class="flIs w80px h80px bgcLightYellow brd1px;solid;#333 mR10px mB10px">
    Inline Start
  </div>
  <div class="flIe w80px h80px bgcLightPink brd1px;solid;#333 mL10px mB10px">
    Inline End
  </div>
  <p>
    This text flows around both floated elements. The inline-start floated
    element appears on the left in LTR languages, while the inline-end floated
    element appears on the right. These logical properties ensure proper layout
    in different writing directions.
  </p>
</div>
```

![Inline float example](./img/float/inline-float.png)

## Clear Utilities

```css
clr<vCSS > {
  clear: <vCSS>;
}
```

Clear utilities are used to control how elements behave in relation to floated elements. Common clear values include:

- `clrL` - Clear left floats
- `clrR` - Clear right floats
- `clrB` - Clear both left and right floats
- `clrN` - No clearing

**Example with Clear Both:**

```html
<div class="p20px bgcLightGray brd1px;solid;#333">
  <div class="flL w100px h100px bgcLightBlue brd1px;solid;#333 mR10px mB10px">
    Float Left
  </div>
  <div class="flR w100px h100px bgcLightGreen brd1px;solid;#333 mL10px mB10px">
    Float Right
  </div>
  <p>This text wraps around both floated elements.</p>
  <div class="clrB p10px bgcLightCoral brd1px;solid;#333">
    This element clears both floats and appears below them.
  </div>
</div>
```

![Clear both example](./img/float/clear-both.png)

## Practical Usage

Float utilities are commonly used for:

- **Text wrapping around images** - Float images left or right to allow text to wrap around them
- **Simple layouts** - Create basic two-column layouts with floated sidebars
- **Pull quotes** - Float quote boxes to create magazine-style layouts
- **Navigation elements** - Float navigation items to create horizontal menus

**Note:** While float was historically used for layout, modern CSS layout methods like Flexbox and Grid are generally preferred for complex layouts. Float is still useful for its original purpose of wrapping text around elements.

## Comparison

**Example:**

```html
<div class="dF fxdColumn gap20px p20px bgcLightGray brd1px;solid;#333">
  <!-- Float Left -->
  <div class="p15px bgcWhite brd1px;solid;#333">
    <div class="flL w80px h80px bgcLightBlue brd1px;solid;#333 mR10px">
      Left
    </div>
    <p>Text wrapping around left-floated element.</p>
  </div>

  <!-- Float Right -->
  <div class="p15px bgcWhite brd1px;solid;#333">
    <div class="flR w80px h80px bgcLightGreen brd1px;solid;#333 mL10px">
      Right
    </div>
    <p>Text wrapping around right-floated element.</p>
  </div>

  <!-- No Float -->
  <div class="p15px bgcWhite brd1px;solid;#333">
    <div class="flN w80px h80px bgcLightCoral brd1px;solid;#333 mB10px">
      None
    </div>
    <p>Element in normal document flow.</p>
  </div>
</div>
```

![Float comparison example](./img/float/comparison.png)
